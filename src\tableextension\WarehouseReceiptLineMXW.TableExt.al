tableextension 60002 "Warehouse Receipt Line MXW" extends "Warehouse Receipt Line"
{
    fields
    {
        field(60000; "Package Count MXW"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
            FieldClass = FlowField;
            CalcFormula = count("Warehouse Receipt Line Dtl MXW" where("Document No." = field("No."), "Document Line No." = field("Line No.")));
            Editable = false;
        }
        field(60001; "Total Package Quantity MXW"; Decimal)
        {
            Caption = 'Total Package Quantity';
            ToolTip = 'Specifies the total package quantity.';
            FieldClass = FlowField;
            CalcFormula = sum("Warehouse Receipt Line Dtl MXW".Quantity where("Document No." = field("No."), "Document Line No." = field("Line No.")));
            Editable = false;
        }
        field(60002; "Lot No. MXW"; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
            Editable = true;
            trigger OnValidate()
            var
                CannotChangeLotNoErr: Label 'You cannot change the Lot No. when packages have been created.';
            begin
                Rec.CalcFields("Package Count MXW");
                if Rec."Package Count MXW" > 0 then
                    Error(CannotChangeLotNoErr);
            end;
        }
        field(60003; "Item Tracking Info Assignd MXW"; Boolean)
        {
            Caption = 'Item Tracking Info. Assigned';
            ToolTip = 'Specifies whether the item tracking information is assigned.';
        }
        field(60004; "Quality Control Documents MXW"; Integer)
        {
            Caption = 'Quality Control Documents';
            ToolTip = 'Specifies the number of quality control documents.';
            FieldClass = FlowField;
            CalcFormula = count("Quality Control Header QCM" where("Lot No." = field("Lot No. MXW"), "Item No." = field("Item No."), "Variant Code" = field("Variant Code"), Type = const(Purchase), "Source Document No." = field("No."), "Source Document Line No." = field("Line No.")));
            Editable = false;
        }
        field(60005; "Quality Control Doc. No. MXW"; Code[20])
        {
            Caption = 'Quality Control Document No.';
            ToolTip = 'Specifies the Quality Control Document No.';
            Editable = false;
            TableRelation = "Quality Control Header QCM";
        }
        field(60006; "Quality Control Doc. Stat. MXW"; Enum "Quality Control Status QCM")
        {
            Caption = 'Quality Control Document Status';
            ToolTip = 'Specifies the Quality Control Document Status.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Quality Control Header QCM".Status where("No." = field("Quality Control Doc. No. MXW")));
        }
        field(60007; "Expiration Date MXW"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the expiration date for the item.';
            trigger OnValidate()
            var
                CannotChangeExpirationDateErr: Label 'You cannot change the Expiration Date when packages have been created.';
            begin
                Rec.CalcFields("Package Count MXW");
                if Rec."Package Count MXW" > 0 then
                    Error(CannotChangeExpirationDateErr);
            end;
        }
    }

    keys
    {
        key(Key12; "Item Tracking Info Assignd MXW")
        {
        }
    }
}
