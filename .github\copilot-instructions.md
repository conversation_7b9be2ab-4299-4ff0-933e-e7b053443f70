
# GitHub Copilot Instructions for Maxwell Customizations (Business Central AL)

You are an AI assistant specialized in AL (Application Language) development for the Maxwell Customizations Per-Tenant Extension (PTE) for Microsoft Dynamics 365 Business Central 25.0. Follow these guidelines to maximize productivity and code quality in this codebase.

---

## Project Architecture & Structure

- **Source Layout**: All source code is under `src/` with subfolders for `codeunit/`, `page/`, `pageextension/`, `report/`, `table/`, `tableextension/`, etc. Use these folders for new objects.
- **Major Components**:
  - **Codeunits**: Business logic, e.g., `MaxwellPurchaseMngtMXW.Codeunit.al`, `MaxwellProductionMngtMXW.Codeunit.al`
  - **Tables/TableExtensions**: Custom data and extensions to standard tables, e.g., `PackageCreationMXW.Table.al`
  - **Pages/PageExtensions**: UI and customizations to standard pages
  - **Reports/ReportLayouts**: Custom reporting
- **ID Range**: Use object IDs in the 60000-60999 range.

## Build, Test, and Debug

- **Build**: Use VS Code command palette: `AL: Package` (Ctrl+Shift+P → "AL: Package"). Ensure symbols are downloaded (`AL: Download Symbols`) before building.
- **Environment**: Target is the "Maxwell-Sandbox" BC 25.0 environment. Tenant ID and environment are preconfigured in `launch.json`.
- **Analyzers**: CodeCop, PerTenantExtensionCop, and UICop are enabled. AppSourceCop is intentionally disabled.
- **Custom Ruleset**: See `custom.ruleset.json` for project-specific analyzer rules (based on Stefan Maron's PTE best practices).

## Naming, Conventions, and Patterns

- **Object Naming**: Max 30 characters, PascalCase, descriptive but concise. Use the namespace pattern: `MXW.Maxwell.FeatureName`.
- **Variable Order**: Declare variables in this order: Record, Report, Codeunit, XmlPort, Page, Query, Notification, then system types, then simple types, then complex types, then collections.
- **Procedure Calls**: Always use brackets, even with no parameters (e.g., `MyProcedure()`).
- **Event Subscribers**: Use signatures without quotes around event names.
- **Record Operations**: Always use explicit parameters (e.g., `Insert(false)` to skip validation).
- **JSON**: Use AL's built-in JSON types and direct Get methods (e.g., `GetText()`).
- **TempBlob**: Use streams directly from `CreateInStream()`/`CreateOutStream()` in method calls.
- **Multiline Text**: Use AL's `@'...'` syntax for multiline text.
- **Conditionals**: Use `else if` as separate `else` and `if` blocks; prefer `case true of` for multiple conditions.

## UI & Application Area

- **Tooltips**: Apply at the table field level, not on page controls.
- **ApplicationArea**: Set at object level, except for field-level in PageExtension/TableExtension.

## Integration & Data Flow

- **Package/Production/Quality Control**: Data flows between custom tables (e.g., `Package Creation MXW`, `Quality Control Header QCM`) and standard BC tables via codeunits.
- **No. Series**: Number series are managed via the `No. Series` codeunit and referenced in setup tables.
- **Cross-Component**: Use codeunits for business logic orchestration; page extensions for UI integration.

## Examples

- See `src/codeunit/MaxwellPurchaseMngtMXW.Codeunit.al` for patterns on record validation, number series, and event handling.
- See `src/page/MaxwellSetupMXW.Page.al` for setup/configuration patterns.

---

**For any new code, follow the above conventions and reference existing files for examples of project-specific patterns.**

---
