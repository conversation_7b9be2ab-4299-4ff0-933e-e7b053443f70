codeunit 60007 "Maxwell Package Trans. Mgt MXW"
{
    SingleInstance = true;

    procedure ProcessBarcode(var PackageTransferHeader: Record "Package Transfer Header MXW")
    var
        PackageNoInformation: Record "Package No. Information";
        PackageTransferLine: Record "Package Transfer Line MXW";
        PackageAlreadyReadErr: Label 'This package has already been read in this package transfer order, please try a different package.';
        PackageNotFoundErr: Label 'Package No.: %1 not found.', Comment = '%1="PackageTransferHeader.Barcode"';
    begin
        if PackageTransferHeader.Barcode = '' then
            exit;

        PackageNoInformation.SetRange("Package No.", PackageTransferHeader.Barcode);
        if not PackageNoInformation.FindFirst() then
            Error(PackageNotFoundErr, PackageTransferHeader.Barcode);

        // Check if package already exists in this transfer order
        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.SetRange("Package No.", PackageTransferHeader.Barcode);
        if not PackageTransferLine.IsEmpty() then
            Error(PackageAlreadyReadErr);

        CreatePackageTransferLineFromPackageNoInfo(PackageTransferHeader, PackageNoInformation);

        PackageTransferHeader.Barcode := '';
    end;

    procedure ShipAndReceivePackageTransferHeader(var PackageTransferHeader: Record "Package Transfer Header MXW")
    var
        PackageTransferLine: Record "Package Transfer Line MXW";
        AtleastErr: Label 'You have to transfer at least one unit of: %1', Comment = '%1="Item No."';
    begin
        PackageTransferHeader.Validate(Shipped, true);
        PackageTransferHeader.Modify(true);

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();
        repeat
            if not (PackageTransferLine."Quantity To Transfer" > 0) then
                Error(AtleastErr, PackageTransferLine."Item No.");
        until PackageTransferLine.Next() = 0;

        CreateAndPostItemReclassificationJournal(PackageTransferHeader, false);
    end;

    local procedure CreatePackageTransferLineFromPackageNoInfo(var PackageTransferHeader: Record "Package Transfer Header MXW"; var PackageNoInformation: Record "Package No. Information")
    var
        PackageTransferLine: Record "Package Transfer Line MXW";
        Item: Record Item;
    begin
        PackageNoInformation.CalcFields(Inventory, "Location Code MXW");

        // Handle Transfer-from Code population logic
        if PackageTransferHeader."Transfer-from Code" = '' then begin
            // If header's Transfer-from Code is blank, populate it with package's location
            PackageTransferHeader.Validate("Transfer-from Code", PackageNoInformation."Location Code MXW");
            PackageTransferHeader.Modify(true);
        end else
            // If header's Transfer-from Code is not blank, ensure it matches package's location
            PackageTransferHeader.TestField("Transfer-from Code", PackageNoInformation."Location Code MXW");

        PackageTransferLine.Init();
        PackageTransferLine."Document No." := PackageTransferHeader."No.";
        PackageTransferLine."Package No." := PackageNoInformation."Package No.";
        PackageTransferLine."Item No." := PackageNoInformation."Item No.";
        PackageTransferLine."Variant Code" := PackageNoInformation."Variant Code";
        PackageTransferLine."Lot No." := PackageNoInformation."Lot No. MXW";

        if Item.Get(PackageNoInformation."Item No.") then
            PackageTransferLine.Description := Item.Description;

        PackageTransferLine.Quantity := PackageNoInformation.Inventory;
        PackageTransferLine."Quantity To Transfer" := PackageNoInformation.Inventory;
        PackageTransferLine."Transfer-from Code" := PackageNoInformation."Location Code MXW";
        PackageTransferLine.Insert(true);
    end;

    procedure CreateAndPostItemReclassificationJournal(var PackageTransferHeader: Record "Package Transfer Header MXW"; HideDialog: Boolean)
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        PackageTransferLine: Record "Package Transfer Line MXW";
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
        LineNo: Integer;
        PackSuccessMsg: Label 'Package Transfer Order: %1 Successfully Received', Comment = '%1="Package Transfer Header MXW"."No."';
        LocationSameErr: Label 'Transfer-from and Transfer-to locations cannot be the same.';
    begin
        MaxwellSetup.GetRecordOnce();
        MaxwellSetup.TestField("Package Tran. Jnl. Batch MXW");
        MaxwellSetup.TestField("Package Tran. Jnl. Temp. MXW");

        PackageTransferHeader.TestField("Transfer-to Code");
        PackageTransferHeader.TestField("Transfer-from Code");

        if PackageTransferHeader."Transfer-to Code" = PackageTransferHeader."Transfer-from Code" then
            Error(LocationSameErr);

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        PackageTransferLine.FindSet();

        LastItemJournalLine.SetRange("Journal Template Name", MaxwellSetup."Package Tran. Jnl. Temp. MXW");
        LastItemJournalLine.SetRange("Journal Batch Name", MaxwellSetup."Package Tran. Jnl. Batch MXW");

        if LastItemJournalLine.FindLast() then
            LineNo := LastItemJournalLine."Line No." + 10000
        else
            LineNo := 10000;

        repeat
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := MaxwellSetup."Package Tran. Jnl. Temp. MXW";
            ItemJournalLine."Journal Batch Name" := MaxwellSetup."Package Tran. Jnl. Batch MXW";
            ItemJournalLine.SetUpNewLine(LastItemJournalLine);
            ItemJournalLine."Entry Type" := ItemJournalLine."Entry Type"::Transfer;
            ItemJournalLine."Line No." := LineNo;
            ItemJournalLine.Insert(true);

            ItemJournalLine.Validate("Item No.", PackageTransferLine."Item No.");
            ItemJournalLine.Validate("Variant Code", PackageTransferLine."Variant Code");
            ItemJournalLine.Validate("Location Code", PackageTransferHeader."Transfer-from Code");
            ItemJournalLine.Validate("New Location Code", PackageTransferHeader."Transfer-to Code");
            ItemJournalLine.Validate(Quantity, PackageTransferLine."Quantity To Transfer");
            ItemJournalLine.Modify(true);
            MaxwellPurchaseManagement.AssignLotNoToItemJournalLine(ItemJournalLine, PackageTransferLine."Lot No.", ItemJournalLine."Quantity (Base)", ItemJournalLine.Quantity, PackageTransferLine."Package No.", 0D);

            LineNo += 10000;
            LastItemJournalLine := ItemJournalLine;
        until PackageTransferLine.Next() = 0;

        Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);
        PackageTransferHeader.Validate(Shipped, true);
        PackageTransferHeader.Validate(Received, true);
        PackageTransferHeader.Modify(true);



        if not HideDialog then
            Message(PackSuccessMsg, PackageTransferHeader."No.");
    end;


}
