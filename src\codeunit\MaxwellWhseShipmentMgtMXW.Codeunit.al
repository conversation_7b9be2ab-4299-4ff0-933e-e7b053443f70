codeunit 60008 "Maxwell Whse. Shipment Mgt MXW"
{
    SingleInstance = true;

    procedure ProcessBarcodeForWarehouseShipment(var WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        PackageNoInformation: Record "Package No. Information";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        PackageAlreadyReadErr: Label 'This package has already been read in this warehouse shipment, please try a different package.';
        PackageNotFoundErr: Label 'Package No.: %1 not found.', Comment = '%1="WarehouseShipmentHeader.Barcode Text MXW"';
        NoMatchingShipmentLineErr: Label 'No matching warehouse shipment line found for package %1 with item %2.', Comment = '%1=Package No., %2=Item No.';
        PackageAlreadyShippedErr: Label 'Package %1 has already been shipped.', Comment = '%1=Package No.';
    begin
        if WarehouseShipmentHeader."Barcode Text MXW" = '' then
            exit;

        // Find package information by package number
        PackageNoInformation.SetRange("Package No.", WarehouseShipmentHeader."Barcode Text MXW");
        if not PackageNoInformation.FindFirst() then
            Error(PackageNotFoundErr, WarehouseShipmentHeader."Barcode Text MXW");

        // Check if package has already been read in this shipment
        WarehouseShipmentLineDtl.SetRange("Document No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        if not WarehouseShipmentLineDtl.IsEmpty() then
            Error(PackageAlreadyReadErr);

        // Find matching warehouse shipment line
        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.SetRange("Item No.", PackageNoInformation."Item No.");
        WarehouseShipmentLine.SetRange("Variant Code", PackageNoInformation."Variant Code");
        if not WarehouseShipmentLine.FindFirst() then
            Error(NoMatchingShipmentLineErr, PackageNoInformation."Package No.", PackageNoInformation."Item No.");

        // Check if package is already shipped
        WarehouseShipmentLineDtl.SetRange("Package No.", PackageNoInformation."Package No.");
        WarehouseShipmentLineDtl.SetRange(Shipped, true);
        if not WarehouseShipmentLineDtl.IsEmpty() then
            Error(PackageAlreadyShippedErr, PackageNoInformation."Package No.");

        // Create warehouse shipment line detail
        CreateWarehouseShipmentLineDetail(WarehouseShipmentLine, PackageNoInformation);

        // Clear barcode field for next scan
        WarehouseShipmentHeader."Barcode Text MXW" := '';
        WarehouseShipmentHeader.Modify(true);

        Message('Package %1 successfully added to warehouse shipment.', PackageNoInformation."Package No.");
    end;

    local procedure CreateWarehouseShipmentLineDetail(WarehouseShipmentLine: Record "Warehouse Shipment Line"; PackageNoInformation: Record "Package No. Information")
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        Item: Record Item;
        SalesLine: Record "Sales Line";
    begin
        WarehouseShipmentLineDtl.Init();
        WarehouseShipmentLineDtl."Document No." := WarehouseShipmentLine."No.";
        WarehouseShipmentLineDtl."Document Line No." := WarehouseShipmentLine."Line No.";
        WarehouseShipmentLineDtl."Package No." := PackageNoInformation."Package No.";
        WarehouseShipmentLineDtl."Item No." := PackageNoInformation."Item No.";
        WarehouseShipmentLineDtl."Variant Code" := PackageNoInformation."Variant Code";
        WarehouseShipmentLineDtl.Quantity := PackageNoInformation.Inventory;
        WarehouseShipmentLineDtl."Unit of Measure Code" := WarehouseShipmentLine."Unit of Measure Code";
        WarehouseShipmentLineDtl.Shipped := false;

        // Get item description
        if Item.Get(PackageNoInformation."Item No.") then
            WarehouseShipmentLineDtl."Item Description" := Item.Description;

        // Get lot number and expiration date from package information extension
        WarehouseShipmentLineDtl."Lot No." := PackageNoInformation."Lot No. MXW";
        WarehouseShipmentLineDtl."Expiration Date" := PackageNoInformation."Expiration Date MXW";

        // Try to get sales order number from warehouse shipment line source
        if WarehouseShipmentLine."Source Type" = Database::"Sales Line" then
            if SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.") then
                WarehouseShipmentLineDtl."Sales Order No." := SalesLine."Document No.";

        // Set item tracking info assigned flag
        WarehouseShipmentLineDtl."Item Tracking Info Assignd MXW" := (WarehouseShipmentLineDtl."Lot No." <> '') or (WarehouseShipmentLineDtl."Expiration Date" <> 0D);

        // Set package count and total package quantity
        WarehouseShipmentLineDtl."Package Count" := 1;
        WarehouseShipmentLineDtl."Total Package Qty." := PackageNoInformation.Inventory;

        WarehouseShipmentLineDtl.Insert(true);
    end;

    procedure GetWarehouseShipmentLineDetails(DocumentNo: Code[20]; DocumentLineNo: Integer): Integer
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", DocumentNo);
        WarehouseShipmentLineDtl.SetRange("Document Line No.", DocumentLineNo);
        exit(WarehouseShipmentLineDtl.Count());
    end;

    procedure GetTotalPackageQuantity(DocumentNo: Code[20]; DocumentLineNo: Integer): Decimal
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        TotalQuantity: Decimal;
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", DocumentNo);
        WarehouseShipmentLineDtl.SetRange("Document Line No.", DocumentLineNo);
        if WarehouseShipmentLineDtl.FindSet() then
            repeat
                TotalQuantity += WarehouseShipmentLineDtl.Quantity;
            until WarehouseShipmentLineDtl.Next() = 0;
        exit(TotalQuantity);
    end;
}
