pageextension 60012 "Warehouse Shipment MXW" extends "Warehouse Shipment"
{
    layout
    {
        addafter(General)
        {
            group("Barcode MXW")
            {
                Caption = 'Barcode Reading';
                field("Barcode Text MXW"; Rec."Barcode Text MXW")
                {
                    ApplicationArea = All;
                    ShowCaption = false;
                    ToolTip = 'Scan or enter the package barcode to add it to the warehouse shipment.';

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
            }
        }
    }

    actions
    {
        addfirst("F&unctions")
        {
            action("AssignItemTrackingInfo MXW")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = LotInfo;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Assigns item tracking information to warehouse shipment lines.';

                trigger OnAction()
                begin
                    MaxwellSalesMngt.AssignItemTrackingInformationFromWarehouseShipmentHeader(Rec);
                end;
            }
        }
        addafter("&Print")
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                action("Warehouse Shipment Line Details MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Warehouse Shipment Line Details';
                    Image = ViewDetails;
                    ToolTip = 'View the warehouse shipment line details with package information.';
                    RunObject = Page "Whse. Shipment Line Dtl MXW";
                    RunPageLink = "Document No." = field("No.");
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                }
            }
        }
    }

    var
        MaxwellSalesMngt: Codeunit "Maxwell Sales Mngt. MXW";
}
