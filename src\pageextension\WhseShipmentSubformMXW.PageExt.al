pageextension 60013 "Whse. Shipment Subform MXW" extends "Whse. Shipment Subform"
{
    layout
    {
        addafter(Description)
        {
            field("Package Count MXW"; Rec."Package Count MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the number of packages for this warehouse shipment line.';
            }
            field("Total Package Quantity MXW"; Rec."Total Package Quantity MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the total quantity from all packages for this warehouse shipment line.';
            }
        }
    }

    actions
    {
        addafter("&Line")
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                action("Warehouse Shipment Line Details MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Line Details';
                    Image = ViewDetails;
                    ToolTip = 'View the warehouse shipment line details with package information.';
                    RunObject = page "Whse. Shipment Line Dtl MXW";
                    RunPageLink = "Document No." = field("No."), "Document Line No." = field("Line No.");
                }
            }
        }
    }
}
