pageextension 60064 "Package No. Info List MXW" extends "Package No. Information List"
{
    layout
    {
        addlast(Control1)
        {
            field("Lot No. MXW"; Rec."Lot No. MXW")
            {
                ApplicationArea = All;
            }
            field("Expiration Date MXW"; Rec."Expiration Date MXW")
            {
                ApplicationArea = All;
            }
            field("Inventory MXW"; Rec.Inventory)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quantity on inventory with this line.';
            }
        }
    }

    actions
    {
        addlast(processing)
        {
            action("Print Label MXW")
            {
                ApplicationArea = All;
                Caption = 'Print Label';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Prints the palette label for selected packages.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    Report.Run(Report::"Palette Label MXW", false, false, PackageNoInformation);
                end;
            }
        }
    }
}
