codeunit 60000 "Maxwell Events MXW"
{
    var
    //MaxwellSetup: Record "Maxwell Setup MXW";

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterCreateWhseReceiptHeaderFromWhseRequest, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterCreateWhseReceiptHeaderFromWhseRequest"(var WhseReceiptHeader: Record "Warehouse Receipt Header"; var WarehouseRequest: Record "Warehouse Request"; var GetSourceDocuments: Report "Get Source Documents")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptHeaderFromRequest(WhseReceiptHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterGetSingleInboundDoc, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterGetSingleInboundDoc"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptHeaderFromSingleDoc(WarehouseReceiptHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Get Source Doc. Inbound", OnAfterSetWarehouseRequestFilters, '', false, false)]
    local procedure "Get Source Doc. Inbound_OnAfterSetWarehouseRequestFilters"(var WarehouseRequest: Record "Warehouse Request"; WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    begin
        if WarehouseReceiptHeader."Vendor No. MXW" <> '' then
            WarehouseRequest.SetRange("Destination No.", WarehouseReceiptHeader."Vendor No. MXW");
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purchases Warehouse Mgt.", OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine, '', false, false)]
    // local procedure "Purchases Warehouse Mgt._OnPurchLine2ReceiptLineOnAfterSetQtysOnRcptLine"(var WarehouseReceiptLine: Record "Warehouse Receipt Line"; PurchaseLine: Record "Purchase Line")
    // begin
    //     WarehouseReceiptLine.Quantity := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine."Qty. (Base)" := PurchaseLine."Whse. Rcpt. Qty-to Receive MXW";
    //     WarehouseReceiptLine.Validate("Qty. Received", 0);
    //     WarehouseReceiptLine.Validate("Qty. Outstanding", PurchaseLine."Whse. Rcpt. Qty-to Receive MXW");
    //     WarehouseReceiptLine.Validate("Qty. to Receive", 0);

    //     PurchaseLine."Whse. Rcpt. Qty-to Receive MXW" := 0;
    //     PurchaseLine.Modify(true);
    // end;

    // [EventSubscriber(ObjectType::Report, Report::"Get Source Documents", OnAfterPurchaseLineOnPreDataItem, '', false, false)]
    // local procedure "Get Source Documents_OnAfterPurchaseLineOnPreDataItem"(var Sender: Report "Get Source Documents"; var PurchaseLine: Record "Purchase Line"; OneHeaderCreated: Boolean; WhseShptHeader: Record "Warehouse Shipment Header"; WhseReceiptHeader: Record "Warehouse Receipt Header")
    // begin
    //     PurchaseLine.SetFilter("Whse. Rcpt. Qty-to Receive MXW", '>0');
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Post Receipt", OnAfterCode, '', false, false)]
    local procedure "Whse.-Post Receipt_OnAfterCode"(var WarehouseReceiptHeader: Record "Warehouse Receipt Header"; WarehouseReceiptLine: Record "Warehouse Receipt Line"; CounterSourceDocTotal: Integer; CounterSourceDocOK: Integer)
    begin
        // Post-processing after warehouse receipt posting
        // Can be enhanced with additional business logic as needed
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Whse.-Create Source Document", OnBeforeWhseReceiptLineInsert, '', false, false)]
    local procedure "Whse.-Create Source Document_OnBeforeWhseReceiptLineInsert"(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.ProcessWarehouseReceiptLineOnInsert(WarehouseReceiptLine);
    end;

    [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCopyTrackingSpec, '', false, false)]
    local procedure "Item Tracking Lines_OnAfterCopyTrackingSpec"(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
    begin
        MaxwellPurchaseManagement.HandleItemTrackingLinesOnAfterCopyTrackingSpec(SourceTrackingSpec, DestTrkgSpec);
    end;

    // [EventSubscriber(ObjectType::Page, Page::"Items by Location", OnAfterSetTempMatrixLocationFilters, '', false, false)]
    // local procedure "Items by Location_OnAfterSetTempMatrixLocationFilters"(var Sender: Page "Items by Location"; var TempMatrixLocation: Record Location temporary)
    // begin
    //     // Maxwell: Filter locations if needed
    // end;

    // [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnBeforeGetNeededQty, '', false, false)]
    // local procedure CalcConsumption_OnBeforeGetNeededQty(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    // var
    //     MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    // begin
    //     //MaxwellProductionManagement.HandleCalcConsumptionOnBeforeGetNeededQty(NeededQty, CalcBasedOn, ProdOrderComponent, ProductionOrder, PostingDate, IsHandled);
    // end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OutputJnlExplRoute_OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.HandleOutputJnlExplRouteOnBeforeOutputItemJnlLineInsert(ItemJournalLine, LastOperation);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterInsertItemJnlLine, '', false, false)]
    local procedure OnAfterInsertItemJnlLine(var ItemJournalLine: Record "Item Journal Line")
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.HandleOnAfterInsertItemJnlLine(ItemJournalLine);
    end;

    // [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnAfterCreateConsumpJnlLine, '', false, false)]
    // local procedure CalcConsumption_OnAfterCreateConsumpJnlLine(LocationCode: Code[10]; BinCode: Code[20]; QtyToPost: Decimal; var ItemJournalLine: Record "Item Journal Line")
    // var
    //     MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    // begin
    //     //MaxwellProductionManagement.HandleCalcConsumptionOnAfterCreateConsumpJnlLine(LocationCode, BinCode, QtyToPost, ItemJournalLine);
    // end;

    [EventSubscriber(ObjectType::Table, Database::"Item Ledger Entry", OnAfterInsertEvent, '', false, false)]
    local procedure OnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        MaxwellPurchaseMngt: Codeunit "Maxwell Purchase Mngt. MXW";
        MaxwellSalesMngt: Codeunit "Maxwell Sales Mngt. MXW";
    begin
        MaxwellPurchaseMngt.HandleOnAfterInsertItemLedgerEntry(Rec, RunTrigger);
        MaxwellSalesMngt.HandleOnAfterInsertItemLedgerEntry(Rec, RunTrigger);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnAfterInsertEvent, '', false, false)]
    local procedure OnAfterInsertProductionOrder(var Rec: Record "Production Order"; RunTrigger: Boolean)
    var
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
    begin
        MaxwellProductionManagement.SetDefaultOutputLocationOnProductionOrder(Rec, RunTrigger);
    end;
}
