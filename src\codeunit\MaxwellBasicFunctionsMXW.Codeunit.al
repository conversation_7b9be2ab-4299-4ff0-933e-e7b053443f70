codeunit 60010 "Maxwell Basic Functions MXW"
{
    SingleInstance = true;
    Subtype = Normal;

    procedure GetUserFullNameFromSecurityId(UserSecurityID: Guid): Text[80]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."Full Name");
        exit('');
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;
}
