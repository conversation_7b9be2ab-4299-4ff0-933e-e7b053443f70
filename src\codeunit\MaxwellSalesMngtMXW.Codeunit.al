codeunit 60010 "Maxwell Sales Mngt. MXW"
{
    SingleInstance = true;

    procedure AssignItemTrackingInformationFromWarehouseShipmentLine(WarehouseShipmentLine: Record "Warehouse Shipment Line")
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
        WarehouseShipmentLineDtlForCalc: Record "Whse. Shipment Line Dtl. MXW";
        TotalUnshippedQuantity: Decimal;
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", WarehouseShipmentLine."No.");
        WarehouseShipmentLineDtl.SetRange("Document Line No.", WarehouseShipmentLine."Line No.");
        WarehouseShipmentLineDtl.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseShipmentLineDtl.FindSet(true) then
            repeat
                AssignItemTrackingInformationFromWarehouseShipmentLineDetail(WarehouseShipmentLineDtl);
                WarehouseShipmentLineDtl."Item Tracking Info Assignd MXW" := true;
                WarehouseShipmentLineDtl.Modify(true);
            until WarehouseShipmentLineDtl.Next() = 0;

        WarehouseShipmentLine."Item Tracking Info Assignd MXW" := true;

        // Calculate total unshipped package quantities (exclude already shipped packages)
        WarehouseShipmentLineDtlForCalc.SetRange("Document No.", WarehouseShipmentLine."No.");
        WarehouseShipmentLineDtlForCalc.SetRange("Document Line No.", WarehouseShipmentLine."Line No.");
        WarehouseShipmentLineDtlForCalc.SetRange(Shipped, false);
        WarehouseShipmentLineDtlForCalc.CalcSums(Quantity);
        TotalUnshippedQuantity := WarehouseShipmentLineDtlForCalc.Quantity;

        // Populate Quantity to Ship with total unshipped package quantities
        WarehouseShipmentLine.Validate("Qty. to Ship", TotalUnshippedQuantity);
        WarehouseShipmentLine.Modify(true);
    end;

    procedure AssignItemTrackingInformationFromWarehouseShipmentHeader(WarehouseShipmentHeader: Record "Warehouse Shipment Header")
    var
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        ItemTrackAssignedMsg: Label 'Item Tracking Information has been assigned.';
    begin
        WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
        WarehouseShipmentLine.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseShipmentLine.FindSet(true) then
            repeat
                AssignItemTrackingInformationFromWarehouseShipmentLine(WarehouseShipmentLine);
                WarehouseShipmentLine."Item Tracking Info Assignd MXW" := true;
                WarehouseShipmentLine.Modify(true);
            until WarehouseShipmentLine.Next() = 0;
        Message(ItemTrackAssignedMsg);
    end;

    local procedure AssignItemTrackingInformationFromSalesLine(LotNo: Code[50]; PackageNo: Code[50]; var SalesLine: Record "Sales Line"; AvailabilityDate: Date; QtyToShipBase: Decimal; QtyToShip: Decimal; ExpirationDate: Date)
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        SalesLineReserve: Codeunit "Sales Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        SalesLineReserve.InitFromSalesLine(TempSourceTrackingSpecification, SalesLine);

        // Create tracking specification for the lot
        TempTrackingSpecification.Init();
        TempTrackingSpecification."Entry No." := 1;
        TempTrackingSpecification."Item No." := SalesLine."No.";
        TempTrackingSpecification."Location Code" := SalesLine."Location Code";
        TempTrackingSpecification."Quantity (Base)" := QtyToShipBase;
        TempTrackingSpecification."Qty. to Handle (Base)" := QtyToShipBase;
        TempTrackingSpecification."Qty. to Invoice (Base)" := QtyToShipBase;
        TempTrackingSpecification."Quantity Handled (Base)" := 0;
        TempTrackingSpecification."Quantity Invoiced (Base)" := 0;
        TempTrackingSpecification."Qty. per Unit of Measure" := SalesLine."Qty. per Unit of Measure";
        TempTrackingSpecification."Qtyper Unit of Measure" := SalesLine."Qty. per Unit of Measure";
        TempTrackingSpecification.Quantity := QtyToShip;
        TempTrackingSpecification."Qty. to Handle" := QtyToShip;
        TempTrackingSpecification."Qty. to Invoice" := QtyToShip;
        TempTrackingSpecification."Quantity Handled" := 0;
        TempTrackingSpecification."Quantity Invoiced" := 0;
        TempTrackingSpecification."Source Type" := Database::"Sales Line";
        TempTrackingSpecification."Source Subtype" := SalesLine."Document Type".AsInteger();
        TempTrackingSpecification."Source ID" := SalesLine."Document No.";
        TempTrackingSpecification."Source Ref. No." := SalesLine."Line No.";
        TempTrackingSpecification."Lot No." := LotNo;
        TempTrackingSpecification."Package No." := PackageNo;
        TempTrackingSpecification."Expiration Date" := ExpirationDate;
        TempTrackingSpecification."Warranty Date" := AvailabilityDate;
        TempTrackingSpecification."Variant Code" := SalesLine."Variant Code";
        TempTrackingSpecification."Unit of Measure Code" := SalesLine."Unit of Measure Code";
        TempTrackingSpecification.Insert();

        // Set up item tracking lines page
        ItemTrackingLines.SetSourceSpec(TempSourceTrackingSpecification, AvailabilityDate);
        ItemTrackingLines.SetTempRecord(TempTrackingSpecification);

        // Register the tracking specification
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
    end;

    procedure AssignItemTrackingInformationFromWarehouseShipmentLineDetail(WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW")
    var
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
    begin
        WarehouseShipmentLine.Get(WarehouseShipmentLineDtl."Document No.", WarehouseShipmentLineDtl."Document Line No.");
        
        if WarehouseShipmentLine."Source Type" = Database::"Sales Line" then begin
            SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.");
            SalesHeader.Get(SalesLine."Document Type", SalesLine."Document No.");

            AssignItemTrackingInformationFromSalesLine(
                WarehouseShipmentLineDtl."Lot No.", 
                WarehouseShipmentLineDtl."Package No.", 
                SalesLine, 
                SalesHeader."Posting Date", 
                WarehouseShipmentLineDtl.Quantity, 
                WarehouseShipmentLineDtl.Quantity, 
                WarehouseShipmentLineDtl."Expiration Date"
            );
        end;
    end;

    procedure HandleOnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    begin
        if (Rec."Lot No." = '') then
            exit;

        // Update the "Shipped" field for matching warehouse shipment line details
        WarehouseShipmentLineDtl.SetRange("Item No.", Rec."Item No.");
        WarehouseShipmentLineDtl.SetRange("Lot No.", Rec."Lot No.");
        WarehouseShipmentLineDtl.SetRange("Package No.", Rec."Package No.");

        if not WarehouseShipmentLineDtl.IsEmpty() then
            WarehouseShipmentLineDtl.ModifyAll(Shipped, true, true);
    end;
}
