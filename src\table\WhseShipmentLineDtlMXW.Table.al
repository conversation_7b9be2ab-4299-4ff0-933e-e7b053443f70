table 60003 "Whse. Shipment Line Dtl. MXW"
{
    Caption = 'Warehouse Shipment Line Details';
    DataClassification = CustomerContent;
    LookupPageId = "Whse. Shipment Line Dtl MXW";
    DrillDownPageId = "Whse. Shipment Line Dtl MXW";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number within the document line.';
        }
        field(4; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the package number.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            TableRelation = Item."No.";
            trigger OnValidate()
            var
                Item: Record Item;
                ItemVariant: Record "Item Variant";
            begin
                if Rec."Item No." = '' then begin
                    Rec."Item Description" := '';
                    exit;
                end;

                if Item.Get(Rec."Item No.") then begin
                    // If variant code exists, try to get variant description, otherwise use item description
                    if Rec."Variant Code" <> '' then begin
                        if ItemVariant.Get(Rec."Item No.", Rec."Variant Code") then
                            Rec."Item Description" := ItemVariant.Description
                        else
                            Rec."Item Description" := Item.Description;
                    end else
                        Rec."Item Description" := Item.Description;
                end else
                    Rec."Item Description" := '';
            end;
        }
        field(6; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies a description of the item.';
        }
        field(7; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the quantity of the item.';
        }
        field(8; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(14; "Expiration Date"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the expiration date for the item.';
        }
        field(9; "Item Tracking Info Assignd MXW"; Boolean)
        {
            Caption = 'Item Tracking Info. Assigned';
            ToolTip = 'Specifies whether the item tracking information is assigned.';
        }
        field(10; Shipped; Boolean)
        {
            Caption = 'Shipped';
            ToolTip = 'Specifies whether the item has been shipped.';
        }
        field(11; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code.';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            trigger OnValidate()
            var
                Item: Record Item;
                ItemVariant: Record "Item Variant";
            begin
                if Rec."Item No." = '' then
                    exit;

                if not Item.Get(Rec."Item No.") then
                    exit;

                // If variant code is specified, try to get variant description
                if Rec."Variant Code" <> '' then begin
                    if ItemVariant.Get(Rec."Item No.", Rec."Variant Code") then
                        Rec."Item Description" := ItemVariant.Description
                    else
                        Rec."Item Description" := Item.Description; // Fallback to item description
                end else
                    Rec."Item Description" := Item.Description; // Use item description when no variant
            end;
        }
        field(12; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the unit of measure code.';
            TableRelation = "Unit of Measure".Code;
        }
        field(13; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
            ToolTip = 'Specifies the sales order number.';
        }
        field(15; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
            AllowInCustomizations = Always;
        }
        field(16; "Total Package Qty."; Decimal)
        {
            Caption = 'Total Package Qty.';
            ToolTip = 'Specifies the total package quantity.';
            AllowInCustomizations = Always;
        }
    }

    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key1; Shipped)
        {
        }
    }

    trigger OnInsert()
    var
        WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    begin
        WarehouseShipmentLineDtl.SetRange("Document No.", Rec."Document No.");
        WarehouseShipmentLineDtl.SetRange("Document Line No.", Rec."Document Line No.");
        if WarehouseShipmentLineDtl.FindLast() then
            Rec."Line No." := WarehouseShipmentLineDtl."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        Rec.TestField(Shipped, false);

        if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then
            PackageNoInformation.Delete(true);
    end;
}
